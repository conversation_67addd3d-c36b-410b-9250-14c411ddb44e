/* Custom styles for ESL Video Player */

/* Mode dropdown styling */
.mode-dropdown {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 24px 6px 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  outline: none;
  appearance: none;
  background-image: none;
  position: relative;
}

.mode-dropdown:hover {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
}

.mode-dropdown:focus {
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
}

/* Speed dropdown styling */
.speed-dropdown {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  padding: 4px 20px 4px 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  outline: none;
  appearance: none;
  background-image: none;
  backdrop-filter: blur(4px);
}

.speed-dropdown:hover {
  background: rgba(255, 255, 255, 0.3);
}

.speed-dropdown:focus {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

/* Control button styling */
.control-button {
  background: rgba(255, 255, 255, 0.15);
  border: none;
  border-radius: 50%;
  padding: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.control-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.control-button:disabled:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: none;
  box-shadow: none;
}

/* Special styling for play button */
.play-button {
  background: rgba(255, 255, 255, 0.2);
  padding: 12px;
}

.play-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.15);
}

/* Mode button styling for ESL controls */
.mode-button {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  color: #475569;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mode-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.mode-button:hover::before {
  left: 100%;
}

.mode-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #cbd5e1;
}

.mode-button.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-color: #2563eb;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
}

.mode-button.active:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.5);
}

/* Subtitle overlay styling - Control panel style */
.subtitle-overlay {
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 20px 32px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  max-width: 80%;
  margin: 0 auto;
}

/* Control bar gradient - More visible and modern */
.control-bar {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.7) 40%,
    rgba(0, 0, 0, 0.4) 70%,
    rgba(0, 0, 0, 0.1) 90%,
    transparent 100%
  );
  backdrop-filter: blur(12px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Dropdown arrow styling */
.dropdown-arrow {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  width: 12px;
  height: 12px;
  color: currentColor;
}

/* Custom scrollbar for subtitle section */
.subtitle-section::-webkit-scrollbar {
  width: 6px;
}

.subtitle-section::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.subtitle-section::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.subtitle-section::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mode-dropdown {
    padding: 4px 16px 4px 8px;
    font-size: 10px;
  }

  .speed-dropdown {
    padding: 3px 16px 3px 6px;
    font-size: 11px;
  }

  .control-button {
    padding: 8px;
  }

  .play-button {
    padding: 10px;
  }

  .subtitle-overlay {
    padding: 16px 20px;
    margin: 0 16px;
    max-width: 90%;
  }

  .mode-button {
    padding: 10px 16px;
    font-size: 12px;
  }
}
